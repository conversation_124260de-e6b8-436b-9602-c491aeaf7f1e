<svg width="384" height="384" viewBox="0 0 384 384" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="geminiGrad384" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#06b6d4;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#0891b2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0e7490;stop-opacity:1" />
    </linearGradient>
    <filter id="glow384">
      <feGaussianBlur stdDeviation="19.200000000000003" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background with gradient -->
  <rect x="0" y="0" width="384" height="384" rx="72" fill="url(#geminiGrad384)"/>
  
  <!-- Gemini constellation symbol -->
  <g transform="translate(192,192)" filter="url(#glow384)">
    <!-- Top star shape -->
    <path d="M0,-120 L30,0 L-30,0 Z" fill="white" opacity="0.95"/>
    <!-- Bottom star shape -->
    <path d="M0,120 L-30,0 L30,0 Z" fill="white" opacity="0.85"/>
    <!-- Connecting elements -->
    <circle cx="0" cy="-60" r="18" fill="white" opacity="0.9"/>
    <circle cx="0" cy="60" r="18" fill="white" opacity="0.9"/>
    <line x1="0" y1="-60" x2="0" y2="60" stroke="white" stroke-width="12" opacity="0.7"/>
  </g>
</svg>