<svg width="72" height="72" viewBox="0 0 72 72" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="geminiGrad72" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#06b6d4;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#0891b2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0e7490;stop-opacity:1" />
    </linearGradient>
    <filter id="glow72">
      <feGaussianBlur stdDeviation="3.6" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background with gradient -->
  <rect x="0" y="0" width="72" height="72" rx="14" fill="url(#geminiGrad72)"/>
  
  <!-- Gemini constellation symbol -->
  <g transform="translate(36,36)" filter="url(#glow72)">
    <!-- Top star shape -->
    <path d="M0,-22.5 L5.625,0 L-5.625,0 Z" fill="white" opacity="0.95"/>
    <!-- Bottom star shape -->
    <path d="M0,22.5 L-5.625,0 L5.625,0 Z" fill="white" opacity="0.85"/>
    <!-- Connecting elements -->
    <circle cx="0" cy="-11.25" r="3.375" fill="white" opacity="0.9"/>
    <circle cx="0" cy="11.25" r="3.375" fill="white" opacity="0.9"/>
    <line x1="0" y1="-11.25" x2="0" y2="11.25" stroke="white" stroke-width="2.25" opacity="0.7"/>
  </g>
</svg>