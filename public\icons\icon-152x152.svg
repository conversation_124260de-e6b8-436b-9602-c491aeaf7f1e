<svg width="152" height="152" viewBox="0 0 152 152" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="geminiGrad152" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#06b6d4;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#0891b2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0e7490;stop-opacity:1" />
    </linearGradient>
    <filter id="glow152">
      <feGaussianBlur stdDeviation="7.6000000000000005" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background with gradient -->
  <rect x="0" y="0" width="152" height="152" rx="29" fill="url(#geminiGrad152)"/>
  
  <!-- Gemini constellation symbol -->
  <g transform="translate(76,76)" filter="url(#glow152)">
    <!-- Top star shape -->
    <path d="M0,-47.5 L11.875,0 L-11.875,0 Z" fill="white" opacity="0.95"/>
    <!-- Bottom star shape -->
    <path d="M0,47.5 L-11.875,0 L11.875,0 Z" fill="white" opacity="0.85"/>
    <!-- Connecting elements -->
    <circle cx="0" cy="-23.75" r="7.125" fill="white" opacity="0.9"/>
    <circle cx="0" cy="23.75" r="7.125" fill="white" opacity="0.9"/>
    <line x1="0" y1="-23.75" x2="0" y2="23.75" stroke="white" stroke-width="4.75" opacity="0.7"/>
  </g>
</svg>